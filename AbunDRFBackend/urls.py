"""AbunDRFBackend URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.urls import path
from rest_framework_simplejwt.views import (
    TokenRefreshView, TokenBlacklistView,
)

from mainapp.views import *

urlpatterns = [
    # Simple JWT Auth
    # path('api/frontend/login/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/frontend/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('api/frontend/blacklist/', TokenBlacklistView.as_view(), name='token_blacklist'),

    # APIs for Admin Side
    path('api/admin/authenticate/', admin_authenticate),
    path('api/admin/login/', admin_login),
    path('api/admin/signup/', admin_signup),
    path('api/admin/logout/', admin_logout),
    path('api/admin/dashboard/', admin_dashboard),
    path('api/admin/all-users/', admin_all_users),
    path('api/admin/all-admins/', admin_all_admins),
    path('api/admin/get-user-data/', admin_get_user_data),
    path('api/admin/save-user-data/', admin_save_user_data),
    path('api/admin/save-website-data/', admin_save_website_data),
    path('api/admin/delete-website/', admin_delete_website),
    # path('api/admin/get-content-plan-data/', admin_get_website_content_plan_data),
    # path('api/admin/change-content-plan-status/', admin_change_content_plan_status),
    # path('api/admin/retry-content-plan/', admin_retry_content_plan),
    path('api/admin/get-task-details/', admin_get_task_details),
    path('api/admin/get-ignored-competitor_domains/', admin_get_ignored_competitor_domains),
    path('api/admin/add-competitor-domains-to-ignore/', admin_add_ignored_competitor_domains),
    path('api/admin/remove-ignored-competitor-domains/', admin_remove_ignored_competitor_domains),
    path('api/admin/get-mini-tools-data/', admin_get_mini_tools_data),
    path('api/admin/get-block-domains/', admin_get_blocked_domains),
    path('api/admin/get-block-website-keywords/', admin_get_blocked_website_keywords),
    path('api/admin/get-block-connect-website-keywords/', admin_get_blocked_keywords),
    path('api/admin/add-block-domains/', admin_add_block_domains),
    path('api/admin/add-block-website-keywords/', admin_add_block_website_keywords),
    path('api/admin/remove-block-domains/', admin_remove_block_domains),
    path('api/admin/remove-block-website-keywords/', admin_remove_block_website_keywords),
    path('api/admin/update-article/', admin_update_article),
    path('api/admin/get-article-content/', admin_get_article_content),
    path('api/admin/get-glossary-content/', admin_get_glossary_content),
    path('api/admin/get-keyword-project-data/', admin_get_keyword_project_data),
    path('api/admin/get-glossary-project-data/',admin_get_glossary_data),
    path('api/admin/get-article-titles-for-keyword/', admin_article_titles_for_keywords_api),
    path('api/admin/add-backlink/', admin_add_backlink),
    path('api/admin/get-backlink/', admin_get_backlinks),
    path('api/admin/remove-backlink/', admin_remove_backlinks),
    path('api/admin/toggle-backlink-visibility/', admin_toggle_backlink_visibility),
    path('api/admin/add-coupon/', admin_add_auto_coupon),
    path('api/admin/get-coupons/', admin_get_coupons),
    path('api/admin/remove-coupons/', admin_remove_coupons),
    path('api/admin/enable-coupon/', admin_enable_disable_coupon),
    path('api/admin/get-article-logs/', admin_article_logs),
    path('api/admin/get-user-integrations/', admin_get_user_integrations),
    path('api/admin/remove-user-integrations/', admin_remove_integrations),
    path('api/admin/get-guest-post-finder/', admin_get_guest_post_finder_data),
    path('api/admin/get-reddit-post-finder/', admin_get_reddit_post_finder_data),
    path('api/admin/post-changelog/', admin_post_changelog),
    path('api/admin/edit-changelog/', admin_edit_changelog),
    path('api/admin/delete-changelog/', admin_delete_changelog),
    path('api/admin/retry-stuck-failed-website-scanning-task/', admin_retry_stuck_failed_website_scanning_task),
    path('api/admin/scrape-more-website-pages/', admin_scrape_more_website_pages),
    path('api/admin/generate-article/', admin_article_generation_api),
    path('api/admin/publish-article/', admin_post_article_api),
    path('api/admin/generate-new-featured-image/', admin_generate_new_featured_image),
    path('api/admin/save-context-in-settings/', admin_save_context_in_settings),
    path('api/admin/save-website-settings/', admin_save_website_settings_api),
    path('api/admin/article-settings/', admin_article_settings_page_api),
    path('api/admin/get-latest-contexts/', admin_get_latest_contexts),
    path('api/admin/get-all-articles-with-feedback/', admin_get_articles_with_feedback),
    path('api/admin/get-ai-stats-pages/', admin_get_ai_stats_pages),
    path('api/admin/get-ai-stats-page-data/', admin_get_ai_stats_page_data),
    path('api/admin/get-ai-comparison-pages/', admin_get_ai_comparison_pages),
    path('api/admin/get-ai-comparison-page-data/', admin_get_ai_comparison_page_data),
    path('api/admin/get-ai-share-widgets/', admin_get_ai_share_widgets),
    path('api/admin/get-ai-share-widget-detail/', admin_get_ai_share_widget_detail),

    # Common APIs
    path('api/common/get-changelogs/', get_changelogs),

    # APIs for React App
    # ----- Pages -----
    path('api/frontend/login/', login_api),
    path('api/frontend/signup/', signup_api),
    path('api/frontend/logged-in-base/', logged_in_base_page_api),
    path('api/frontend/signup-plan-selection/', signup_plan_selection_page_api),
    path('api/frontend/logout/', logout_api),
    path('api/frontend/contact-us/', contact_us_page_api),
    path('api/frontend/connect-website/', connect_website_api),
    path('api/frontend/max-websites/', max_websites_page_api),
    # path('api/frontend/content-plan/', content_plan_page_api),
    path('api/frontend/keywords/', keywords_page_api),
    path('api/frontend/articles/', articles_page_api),
    path('api/frontend/articles/edit/', article_edit_page_api),
    path('api/frontend/settings/', settings_page_api),
    path('api/frontend/plans/', plans_page_api),
    path('api/frontend/profile/', profile_page_api),
    path('api/frontend/wordpress-integration-success/', wordpress_integration_success),
    path('api/frontend/google-integrations-success/', google_integrations_success),
    # path('api/frontend/webflow-integration-success/', webflow_integration_success),
    path('api/frontend/indexation/', indexation_page_api),
    path('api/fontend/google-signup-login-auth/', google_signup_login_auth),
    path('api/frontend/keyword-project/keyword-titles/', keyword_project_titles_page_api),
    path('api/frontend/website-scanning/', website_scanning_page_api),
    path('api/frontend/delete-website-scan-data/', delete_website_scan_data),
    path('api/frontend/update-webpage-summary-and-schema/', update_webpage_summary_and_schema),

    # ----- Other APIs -----
    path('api/frontend/verify-email/', email_verification_api),
    path('api/frontend/contact-us/send-message/', contact_us_api),
    path('api/frontend/get-all-plan-data/', get_all_plan_data_api),
    path('api/frontend/get-subscription-history/', get_subscription_history_api),
    path('api/frontend/get-stripe-portal-link/', get_stripe_portal_link_api),
    path('api/frontend/purchase-plan/', purchase_plan_api),
    path('api/frontend/change-plan/', change_plan_api),
    path('api/frontend/use-free-plan/', use_free_plan_api),
    path('api/frontend/card-payment-failed/', card_payment_failed_api),
    path('api/frontend/checkout-success/', checkout_success_api),
    path('api/frontend/forgot-password/', forgot_password_api),
    path('api/frontend/reset-password/', reset_password_api),
    path('api/frontend/check-domain/', check_domain_api),
    path('api/frontend/get-title-description/', website_title_description_api),
    path('api/frontend/get-industry-icp-text/', website_industry_icp_api),
    path('api/frontend/check-keywords/', check_keywords_api),
    path('api/frontend/get-competitors/', competitors_api),
    # path('api/frontend/content-plan-done/', content_plan_done_api),
    # path('api/frontend/retry-content-plan/', retry_content_plan_api),
    # path('api/frontend/get-content-plan-data/', get_content_plan_data_api),
    path('api/frontend/get-article-titles/', article_titles_api),
    path('api/frontend/edit-article-title/', edit_title_api),
    path('api/frontend/post-article/', post_article_api),
    path('api/frontend/get-website-keyword-article-titles/', website_keyword_article_titles_api),
    path('api/frontend/generate-v2-article/', article_generation_v2_api),
    path('api/frontend/get-article-content/', get_article_content_api),
    path('api/frontend/article-is-generated/', article_is_generated_api),
    path('api/frontend/save-article/', save_article_api),
    path('api/frontend/save-website-settings/', save_website_settings_api),
    path('api/frontend/save-email-pref/', save_email_preference_api),
    path('api/frontend/bulk-archive-unarchive-articles/', bulk_archive_unarchive_articles),
    path('api/frontend/bulk-generate-v2-article/', bulk_article_generation_v2_api),
    path('api/frontend/bulk-post-article/', bulk_post_article_api),
    path('api/frontend/get-archive-article-titles/', archive_article_titles_api),
    path('api/frontend/delete-current-website/', delete_website_api),
    path('api/frontend/google-integration-fetch-data/', google_integration_fetch_data_api),
    path('api/frontend/domain-suggestion/', clearbit_domain_suggestion),
    path('api/frontend/send-pages-for-indexing/', send_pages_for_indexing),
    path('api/frontend/fetch-all-pages/', fetch_all_pages),
    path('api/frontend/fetch-pages-sent-for-indexing/', fetch_pages_sent_for_indexing),
    path('api/frontend/fetch-all-indexed-pages/', fetch_all_indexed_pages),
    path('api/frontend/indexation-report-stats/', indexation_report_stats),
    path('api/frontend/fetch-all-not-indexed/', fetch_all_not_indexed),
    path('api/frontend/fetch-all-rejected-page/', fetch_all_rejected_page), 
    path('api/frontend/feature-request/', feature_request_api),
    path('api/frontend/integration-feature-request/', integration_feature_request_api),
    path('api/frontend/schedule-article-auto-publish/', schedule_article_auto_publish),
    path('api/frontend/google-login-signup/success/', google_signup_login_success),
    path('api/frontend/get-backlink/', get_backlinks),
    path('api/frontend/get-webflow-sites/', get_webflow_sites),
    path('api/frontend/get-wordpress-sites/', get_wordpress_sites),
    path('api/frontend/get-wix-sites/', get_wix_sites),
    path('api/frontend/get-shopify-shops/', get_shopify_shops),
    path('api/frontend/get-ghost-sites/', get_ghost_sites),
    path('api/frontend/post-survey/', post_survey),
    path('api/frontend/get-logs/', get_article_progress),
    path('api/frontend/get-longtail-keyword-suggestions/', get_longtail_keyword_suggestions),
    path('api/frontend/get-scraped-webpages/', get_scraped_webpages),
    path('api/frontend/add-or-rescan-webpage/', add_or_rescan_webpage),
    path('api/frontend/post-guest-post-finder-query/', post_guest_post_finder_query),
    path('api/frontend/get-guest-post-finder-queries/', get_guest_post_finder_queries),
    path('api/frontend/get-detail-guest-post-finder-queries/', get_detail_guest_post_finder_queries),
    path('api/frontend/save-context-in-settings/', save_context_in_settings),
    path('api/frontend/get-latest-contexts/', get_latest_contexts),
    path('api/frontend/fetch-competitors-v2/', competitors_api_v2),
    path('api/frontend/check-k8-job-status/', check_k8_job_status),


    # ----- New APIs for V2 -----
    path('api/frontend/get-google-suggestions/', get_google_suggestions),
    path('api/frontend/generate-titles-from-keyword/', generate_v2_titles_from_keyword_api),
    path('api/frontend/remove-keyword-project-keywords/',remove_keyword_project_keywords_api),
    path('api/frontend/create-custom-title-for-keyword/', generate_custom_title_for_keyword_api),
    path('api/frontend/create-custom-keyword/', generate_custom_keyword_for_keyword_api),
    path('api/frontend/generate-titles-from-gsc-keyword/', generate_v2_titles_from_gsc_keyword_api),
    path('api/frontend/cancel-user-subscription/', cancel_user_subscription),

    # --------- integration urls ---------
    path('api/frontend/wp-integration/', wordpress_integration_auth),
    path('api/frontend/wp-category-integration/', wordpress_integration_category_auth),
    path('api/frontend/wp-add-category/', wordpress_add_category),
    path('api/frontend/wp-post-category-checked/', wordpress_post_category_checked),
    path('api/frontend/wp-get-category-checked/', wordpress_get_category_checked),
    path('api/frontend/webflow-integration/', webflow_integration_auth),
    path('api/frontend/google-integrations/', google_integrations_auth),
    path('api/frontend/remove-all-integrations/', remove_all_integrations_api),
    path('api/frontend/wix-integration/', wix_integration_auth),
    path('api/frontend/shopify-integration/', shopify_integration),
    path('api/frontend/ghost-integration/', ghost_integration_auth),
    path('api/frontend/twya-submit/', twya_submit),

    # ------------------------------------
    path('api/frontend/save-website-details/', save_website_details_api),
    path('api/frontend/save-user-details/', save_user_details_api),
    path('api/frontend/upload-domain-logo/', upload_domain_logo),
    path('api/frontend/switch-active-website/', switch_active_website_api),
    path('api/frontend/resend-verification-email/', resend_verification_email_api),
    path('api/frontend/save-website-industry/', save_website_industry_api),
    path('api/frontend/save-website-icp/', save_website_icp_api),
    path('api/frontend/download/article/', download_article),
    path('api/frontend/get-competitor-domains/', get_competitor_domains),
    path('api/frontend/remove-competitors/', remove_competitors),
    path('api/frontend/add-competitors/', add_competitors),
    path('api/frontend/add-competitor/', add_competitor),
    path('api/frontend/get-competitors-research-data/', get_competitor_research_data),
    path('api/frontend/generate-competitor-keywords/', generate_keywords_for_competitors),
    path('api/frontend/get-competitor-research-keywords-data/', get_competitor_research_keywords_data),
    path('api/frontend/rename-keyword-project/', rename_keyword_project),

    # ----- APIs for K8 -----
    path('api/k8/filter-existing-keywords/', filter_existing_keywords),
    path('api/k8/log-k8-job-messages/', log_k8_job_messages),
    path('api/k8/update-content-plan-progress/', update_content_plan_progress),
    path('api/k8/save-article-image-k8/', save_article_image_k8),
    path('api/k8/save-segment-article-image-k8/', save_segment_article_image_k8),
    path('api/k8/fetch-article-internal-links/', fetch_article_internal_links),
    path('api/k8/fetch-task-data/', fetch_task_data, name='api-k8-fetch-task-data'),
    path('api/k8/save-task-data/', save_task_data),

    # ----- Webhooks -----
    path('wh/stripe/', stripe_webhook, name='wh-stripe'),
    # path('wh/k8/content-plan/', content_plan_webhook, name='wh-k8-content-plan'),
    path('wh/k8/article-generation/', article_generation_webhook, name='wh-k8-article-generation'),
    path('wh/k8/website-scanning/', website_scanning_webhook, name='wh-k8-website-scanning'),
    path('wh/k8/article-internal-link/', article_internal_link_webhook, name='wh-k8-article-internal-link'),
    path('wh/k8/competitor-finder/', competitor_finder_webhook, name='wh-k8-competitor-finder'),
    path('wh/k8/ai-calculator/', ai_calculator_webhook, name='wh-k8-ai-calculator'),
    path('wh/k8/stats-generation/', stats_generation_webhook, name='wh-k8-stats-generation'),
    path('wh/appsumo/', appsumo_webhook, name='wh-appsumo'),

    # ----- Mini AI Tools API -----
    path('api/frontend/mini-tool/ai-meta-desc-generator/', ai_meta_desc_generator, name="ai-meta-desc-generator"),
    path('api/frontend/mini-tool/ai-rewording/', ai_rewording, name="ai-rewording"),
    path('api/frontend/mini-tool/ai-blog-title-generator/', ai_blog_title_generator, name="ai-blog-title-generator"),
    path('api/frontend/mini-tool/ai-content-idea-generator/', ai_content_idea_generator, name="ai-content-idea-generator"),
    path('api/frontend/mini-tool/ai-social-media-bio-generator/', ai_social_media_bio_generator, name="ai-social-media-bio-generator"),
    path('api/frontend/mini-tool/ai-social-media-caption-generator/', ai_social_media_caption_generator, name="ai-social-media-caption-generator"),
    path('api/frontend/mini-tool/ai-social-media-hashtag-generator/', ai_social_media_hashtag_generator, name="ai-social-media-hashtag-generator"),
    path('api/frontend/mini-tool/ai-social-media-username-generator/', ai_social_media_username_generator, name="ai-social-media-username-generator"),
    path('api/frontend/mini-tool/ai-youtube-video-title-generator/', ai_youtube_video_title_generator, name="ai-youtube-video-title-generator"),
    path('api/frontend/mini-tool/ai-brand-name-generator/', ai_brand_name_generator, name="ai-brand-name-generator"),
    path('api/frontend/mini-tool/ai-business-name-generator/', ai_business_name_generator, name="ai-business-name-generator"),
    path('api/frontend/mini-tool/ai-li-post-idea-generator/', ai_li_posts_idea_generator, name="ai-li-post-idea-generator"),
    path('api/frontend/mini-tool/ai-color-psy-generator/', ai_color_psy_generator, name="ai-color-psy-generator"),
    path('api/frontend/mini-tool/ai-job-desc-generator/', ai_job_desc_generator, name="ai-job-desc-generator"),
    path('api/frontend/mini-tool/ai-story-generator/', ai_story_generator, name="ai-story-generator"),
    path('api/frontend/mini-tool/ai-business-desc-generator/', ai_business_desc_generator, name="ai-business-desc-generator"),
    path('api/frontend/mini-tool/ai-business-slogan-generator/', ai_business_slogan_generator, name="ai-business-slogan-generator"),
    path('api/frontend/mini-tool/ai-nda-generator/', ai_nda_generator, name="ai-nda-generator"),
    path('api/frontend/mini-tool/ai-essay-generator/', ai_essay_generator, name="ai-essay-generator"),
    path('api/frontend/mini-tool/domain-authority-checker/', domain_authority_checker, name="domain-authority-checker"),
    path('api/frontend/mini-tool/ai-keyword-generator/', ai_keyword_generator, name="ai-keyword-generator"),
    path('api/frontend/mini-tool/ai-video-script-generator/', ai_video_script_generator, name="ai-video-script-generator"),
    path('api/frontend/mini-tool/ai-blog-meta-desc-generator/', ai_blog_meta_desc_generator, name="ai-blog-meta-desc-generator"),
    path('api/frontend/mini-tool/ai-article-outline-generator/', ai_article_outline_generator, name="ai-article-outline-generator"),
    path('api/frontend/mini-tool/ai-company-bio-generator/', ai_company_bio_generator, name="ai-company-bio-generator"),

    # ----- New Keywords Research APIs V2 -----
    path('api/frontend/keyword-projects/', keyword_projects_page_api),
    path('api/frontend/get-keyword-projects/', get_keyword_projects),
    path('api/frontend/get-keyword-project/', get_keyword_project),
    path('api/frontend/upload-keywords-v2/', upload_keywords_api_v2),
    path('api/frontend/get-keyword-project-data/', get_keyword_project_data),
    path('api/frontend/ai-keywords-research-api/', ai_keywords_research_api),
    path('api/frontend/google-integration-fetch-connected-domains/', google_integration_fetch_connected_domains_api),
    path('api/frontend/google-integration-fetch-data-from-domain/', google_integration_fetch_data_from_domain_api),
    path('api/frontend/remove-keyword/', remove_keyword),
    path('api/frontend/remove-keyword-project/', remove_keyword_project),

    # ----- New Automation Project Page APIs -----
    path('api/frontend/automation-projects/', automation_projects),
    path('api/frontend/get-automation-projects/', get_automation_projects),
    path('api/frontend/save-automation-project/', save_automation_project_api),
    path('api/frontend/get-automation-project-data/', get_automation_project_data),
    path('api/frontend/update-automation-project/', update_automation_project_api),
    path('api/frontend/delete-automation-project/', delete_automation_project_api),
    path('api/frontend/get-generated-articles-for-automation-project/', get_generated_articles_for_automation_project),

    # ----- Content Calendar APIs -----
    path('api/frontend/get-scheduled-articles/', fetch_scheduled_articles),

    # ----- Progress Bar polling API -----
    path('api/frontend/get-task-progress/', get_celery_task_progress),

    # ----- New Article Edit Page APIs -----
    path('api/frontend/remove-featured-image/', remove_featured_image),
    path('api/frontend/generate-new-featured-image/', generate_new_featured_image),
    path('api/frontend/upload-featured-image/', upload_featured_image),
    path('api/frontend/generate-ai-streaming-token/', generate_ai_streaming_token),
    path('api/frontend/stream-ai-response/<str:token>/', stream_ai_response),

    # ----  Create Article Page API ---#
    path('api/frontend/fetch-serper-title-data/', fetch_serper_title_api),
    path('api/frontend/new-article-data/', new_article_data_api),
    path('api/frontend/fetch-creative-title-data/', fetch_creative_title_api),
    path('api/frontend/user-verified/', user_verified_api),
    path('api/frontend/create-article', create_article_page_api),

    # ---  New Article SignUp Redirect Page --- #
    path('api/frontend/get-title-data/', generate_titles_api_for_logout_users),

    # --- Programmatic SEO --- #
    path('api/frontend/generate-seo-titles/', programmatic_seo_titles),
    path('api/frontend/get-seo-titles/', get_all_seo_projects),

    # --- Appsumo License Activation ---
    path('api/frontend/add-appsumo-license-to-user-account/', add_appsumo_license_to_user_account),

    # --- Blog Author Finder API --- #
    path('api/frontend/validate-blog-urls/', validate_blog_urls),
    path('api/frontend/get-blog-data/', get_blog_data),
    path('api/frontend/get-blog-bulk-data/', get_blog_bulk_data),
    path('api/frontend/download-csv/', download_blog_data_as_csv),

    # --- Glossary Topic ---
    path('api/frontend/generate-glossary-topic/', generate_glossary_topic),
    path('api/frontend/get-glossary-topic/', get_glossary_topic),
    path('api/frontend/get-all-glossary-projects/', get_all_glossary_projects),
    path('api/frontend/update-glossary-words/', update_glossary_words),
    path('api/frontend/generate-glossary-content/', generate_bulk_glossary_content),
    path('api/frontend/resume-bulk-glossary-content/', resume_bulk_glossary_content),
    path('api/frontend/get-glossary-terms/', Get_Glossary_Terms),
    path('api/frontend/get-glossary-by-keyword-hash/', Get_Glossary_By_Keyword_Hash),
    path('api/frontend/post-glossary-api/', post_glossary_api),
    path('api/frontend/save-glossary-api/', save_glossary_api),
    path('api/frontend/schedule-glossary-auto-publish/', schedule_glossary_auto_publish),
    path('api/frontend/edit-glossary-term-api/', edit_glossary_term_api),
    path('api/frontend/bulk-post-glossary-api/', bulk_post_glossary_api),
    path('api/frontend/bulk-archive-unarchive-glossaries/', bulk_archive_unarchive_glossaries),

    # --- Reddit Top Ranked Post Finder ---
    path('api/frontend/post-reddit-finder-query/', post_reddit_finder_query),
    path('api/frontend/get-reddit-post-finder/', get_reddit_post_finder_queries),
    path('api/frontend/get-detail-reddit-post-finder/', get_detail_reddit_post_finder_queries),

    # --- Article language preference ---
    path('api/frontend/get-article-language-preference/', get_article_language_preference),

    # --- Website Scanning ---
    path('api/frontend/add-sitemap/', add_sitemap),
    path('api/frontend/find-website-sitemaps/', find_website_sitemaps),
    path('api/frontend/get-website-analysis-stats/', get_website_analysis_stats),
    path('api/frontend/rescrape-website-pages/', rescrape_website_pages),
    path('api/frontend/scrape-more-website-pages/', scrape_more_website_pages),
    path('api/frontend/retry-stuck-failed-website-scanning-task/', retry_stuck_failed_website_scanning_task),
    path('api/frontend/toggle-auto-scan-website/', toggle_auto_scan_website_api),

    # --- AI Auto Schema ---
    path('api/frontend/toggle-auto-schema/', toggle_auto_schema_api),
    path('api/frontend/update-webpage-schema-setting/', update_webpage_schema_setting),
    path('api/frontend/bulk-update-webpage-schema-setting/', bulk_update_webpage_schema_setting),
    path('api/frontend/generate-webpages-schema/', generate_webpages_schema),
    path('api/frontend/mark-tool-as-used/', mark_tool_as_used),

    # --- AI Calculator ---
    path('api/frontend/generate-calculator-embed-data/', generate_calculator_embed_data),
    path('api/frontend/get-calculator-embed-script/', get_calculator_embed_script),
    path('api/frontend/get-calculator-code/<str:calculator_id>', get_calculator_code),
    path('api/frontend/ai-html-calculator-generator/', generate_html_calculator_code),
    path('api/frontend/ai-html-calculator-modifier/', modify_html_calculator_code),
    path('api/frontend/get-ai-calculators/', get_ai_calculators),
    path('api/frontend/get-ai-calculator-data/', get_ai_calculator_data),

    # --- Article Schedule Calender ---
    path('api/frontend/get-scheduled-calendar-articles/', scheduled_calendar_article_api),
    path('api/frontend/delete-scheduled-calendar-article/', delete_scheduled_calendar_article),

    # --- Selected GSC Domain ---
    path('api/frontend/post-selected-gsc-domain/', post_selected_gsc_domain),

    # --- GHL Integration ---
    path('api/frontend/initiate-ghl-auth/', initiate_ghl_auth),
    path('oauth/callback/', ghl_oauth_callback),
    path('api/frontend/get-ghl-sites/', get_ghl_sites),
    path('api/frontend/add-ghl-category/', add_ghl_category),
    path('api/frontend/get-ghl-categories/', get_ghl_categories),
    path('api/frontend/ghl-get-category-checked/', ghl_get_category_checked),

    # --- Optimized published articles ---
    path('api/frontend/fetch-wp-published-article/', trigger_fetch_wp_articles),
    path('api/frontend/get-all-published-article/', get_all_published_article),

    # --- Search image ---
    path("api/frontend/fetch_search_image/", fetch_search_image),
    path("api/frontend/save-include-linking/", save_include_linking),
    
    # --- Tools Embedding ---
    # Get the tools integration script which user can add to their website
    path("api/frontend/get-tools-integration-script/", get_tools_integration_script),
    # Get the tools loading script which will be loaded on the website (single script to load all tools)
    path("api/frontend/get-tools-loading-script/", get_tools_loading_script),
    # Loaded script on website will make a call to this endpoint to load all tools
    path("api/frontend/load-tools-scripts/", load_tools_scripts),
    # Verify the tools loading script is added to the website
    path("api/frontend/verify-tools-loading-script/", verify_tools_loading_script),
    
    # --- ICP to KW --- 
    path("api/frontend/icp-to-kw-research/", icp_to_kw_research),
    path("api/frontend/fetch-volume-for-kw/", fetch_volume_for_kw),
    
    # --- Youtube to Article Gen --- 
    path("api/frontend/fetch-yt-title/", get_yt_video_title),    

    # --- AI Stats Page ---
    path("api/frontend/generate-ai-stat-ideas/", generate_ai_stat_ideas), 
    path("api/frontend/generate-ai-stats-page/", generate_ai_stats_page), 
    path("api/frontend/get-stats-page-data/", get_stats_page_data), 
    path("api/frontend/modify-html-stats-page-code/", modify_html_stats_page_code),
    path("api/frontend/get-updated-stats-data/<str:task_id>/", get_updated_stats_page), 
    path("api/frontend/get-ai-stats-pages/", get_ai_stats_pages),
    path("api/frontend/get-ai-stats-page-data/", get_ai_stats_page_data),
    path('api/frontend/set-current-stats-version/', set_current_stats_version),
    path("api/frontend/delete-stats-page-version/<int:version_id>/", delete_stats_page_version),
    path('api/frontend/generate-stats-script-and-div-tag/', generate_stats_script_and_div_tag),
    path('api/frontend/get-universal-stats-script/', get_universal_stats_script),
    path('api/frontend/get-stats-html/<str:stats_id>/', get_stats_html),
    path('api/frontend/get-stats-jsonp/<str:stats_id>/', get_stats_jsonp),


    # --- AI Comparison Page ---
    path('api/frontend/generate-ai-comparison-page/', generate_ai_comparison_page),
    path('api/frontend/get-comparison-page-data/', get_comparison_page_data),
    path('api/frontend/modify-html-comparison-page-code/', modify_html_comparison_page_code),
    path('api/frontend/get-updated-comparison-data/<str:task_id>/', get_updated_comparison_data),
    path('api/frontend/set-current-comparison-version/', set_current_comparison_version),
    path('api/frontend/get-ai-comparison-pages/', get_ai_comparison_pages),
    path('api/frontend/get-ai-comparison-page-data/', get_ai_comparison_page_data),
    path('api/frontend/delete-comparison-page-version/', delete_comparison_page_version),
    path('api/frontend/generate-comparison-script-and-div-tag/', generate_comparison_script_and_div_tag),
    path('api/frontend/get-universal-comparison-script/', get_universal_comparison_script),
    path('api/frontend/get-comparison-html/<str:comp_id>/', get_comparison_html),
    path('api/frontend/get-comparison-jsonp/<str:comp_id>/', get_comparison_jsonp),

    # --- AI Share Widget ---
    path('api/frontend/create-ai-share-widget/', create_ai_share_widget),
    path('api/frontend/get-ai-share-widgets/', get_ai_share_widgets),
    path('api/frontend/get-ai-share-widget-detail/<str:widget_id>/', get_ai_share_widget_detail),
    path('api/frontend/share/', share_redirect),
    path('api/frontend/generate-share-widget-script-and-div-tag/', generate_share_widget_script_and_div_tag),
    path('api/frontend/get-share-widget-jsonp/<str:widget_id>/', get_share_widget_jsonp),
    path('api/frontend/ai-widgets/', ai_widgets),
    path('api/frontend/get-share-widget-html/<str:widget_id>/', get_share_widget_html),
]
